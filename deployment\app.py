"""
FinBERT Financial Sentiment Analysis Flask Application
Advanced interface for financial text sentiment analysis with explanations and summaries
"""

from flask import Flask, render_template, request, jsonify
import torch
import numpy as np
from transformers import AutoModelForSequenceClassification, AutoTokenizer
import re
import os
import json
from datetime import datetime
import logging
from typing import Dict, List, Tuple
import nltk
from collections import Counter
import warnings

# Download required NLTK data
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('vader_lexicon', quiet=True)
except:
    pass

warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class AdvancedFinBERTAnalyzer:
    def __init__(self, model_path):
        """Initialize the advanced FinBERT analyzer with enhanced capabilities."""
        self.model = None
        self.tokenizer = None
        self.label_mapping = {0: 'negative', 1: 'neutral', 2: 'positive'}
        self.reverse_label_mapping = {'negative': 0, 'neutral': 1, 'positive': 2}
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.max_length = 512
        
        # Financial sentiment keywords for explanation
        self.positive_keywords = [
            'profit', 'growth', 'increase', 'gain', 'rise', 'surge', 'boost', 'strong',
            'excellent', 'outperform', 'beat', 'exceed', 'improvement', 'success',
            'bullish', 'optimistic', 'positive', 'recovery', 'expansion', 'momentum'
        ]
        
        self.negative_keywords = [
            'loss', 'decline', 'decrease', 'fall', 'drop', 'crash', 'weak', 'poor',
            'disappointing', 'underperform', 'miss', 'shortfall', 'concern', 'risk',
            'bearish', 'pessimistic', 'negative', 'recession', 'crisis', 'volatility'
        ]
        
        self.neutral_keywords = [
            'stable', 'maintain', 'unchanged', 'steady', 'consistent', 'hold',
            'forecast', 'expect', 'estimate', 'project', 'outlook', 'guidance'
        ]
        
        self.load_model(model_path)
    
    def load_model(self, model_path):
        """Load the fine-tuned FinBERT model."""
        try:
            logger.info(f"Loading FinBERT model from {model_path}")
            
            # Load model and tokenizer
            self.model = AutoModelForSequenceClassification.from_pretrained(model_path)
            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            
            # Move model to device
            self.model.to(self.device)
            self.model.eval()
            
            # Load model info if available
            model_info_path = os.path.join(model_path, 'model_info.json')
            if os.path.exists(model_info_path):
                with open(model_info_path, 'r') as f:
                    model_info = json.load(f)
                    self.max_length = model_info.get('max_length', 512)
                    logger.info(f"Model info loaded: max_length={self.max_length}")
            
            logger.info(f"FinBERT model loaded successfully on {self.device}")
            logger.info(f"Model vocabulary size: {self.tokenizer.vocab_size}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def clean_text(self, text: str) -> str:
        """Clean and preprocess financial text."""
        if not text:
            return ""
        
        # Basic cleaning
        text = str(text).strip()
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # Fix common encoding issues
        text = text.replace('â€™', "'")
        text = text.replace('â€œ', '"')
        text = text.replace('â€�', '"')
        
        # Clean up multiple spaces
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def extract_key_phrases(self, text: str, sentiment: str) -> List[str]:
        """Extract key phrases that influenced the sentiment classification."""
        text_lower = text.lower()
        key_phrases = []
        
        # Get relevant keywords based on sentiment
        if sentiment == 'positive':
            keywords = self.positive_keywords
        elif sentiment == 'negative':
            keywords = self.negative_keywords
        else:
            keywords = self.neutral_keywords
        
        # Find matching keywords in text
        for keyword in keywords:
            if keyword in text_lower:
                # Extract surrounding context (3 words before and after)
                pattern = r'\b\w+\s+\w+\s+\w*' + re.escape(keyword) + r'\w*\s+\w+\s+\w+\b'
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    key_phrases.append(match.strip())
                
                # If no context found, just add the keyword
                if not matches:
                    key_phrases.append(keyword)
        
        return key_phrases[:5]  # Return top 5 key phrases
    
    def generate_summary(self, text: str) -> str:
        """Generate a concise summary of the financial text."""
        sentences = nltk.sent_tokenize(text)
        
        if len(sentences) <= 2:
            return text
        
        # Simple extractive summarization
        # Priority: sentences with financial keywords
        financial_terms = [
            'revenue', 'profit', 'earnings', 'stock', 'market', 'financial',
            'quarter', 'growth', 'investment', 'company', 'business'
        ]
        
        sentence_scores = []
        for sentence in sentences:
            score = 0
            sentence_lower = sentence.lower()
            
            # Score based on financial terms
            for term in financial_terms:
                if term in sentence_lower:
                    score += 1
            
            # Score based on length (prefer medium-length sentences)
            word_count = len(sentence.split())
            if 10 <= word_count <= 30:
                score += 1
            
            sentence_scores.append((sentence, score))
        
        # Sort by score and get top sentences
        sentence_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Select top 2-3 sentences for summary
        max_sentences = min(3, max(2, len(sentences) // 3))
        top_sentences = [s[0] for s in sentence_scores[:max_sentences]]
        
        return ' '.join(top_sentences)
    
    def explain_sentiment(self, text: str, sentiment: str, confidence: float, key_phrases: List[str]) -> str:
        """Generate an explanation for the sentiment classification."""
        explanations = {
            'positive': [
                "The text contains positive financial indicators and optimistic language.",
                "Keywords suggest favorable market conditions or business performance.",
                "The language indicates growth, profits, or positive market sentiment."
            ],
            'negative': [
                "The text contains negative financial indicators and pessimistic language.",
                "Keywords suggest unfavorable market conditions or business challenges.",
                "The language indicates losses, declines, or negative market sentiment."
            ],
            'neutral': [
                "The text presents balanced or factual information without strong sentiment.",
                "The language is objective and doesn't lean heavily positive or negative.",
                "Keywords suggest stable conditions or neutral market outlook."
            ]
        }
        
        base_explanation = explanations[sentiment][0]
        
        # Add confidence context
        if confidence > 0.8:
            confidence_text = "The model is highly confident in this classification."
        elif confidence > 0.6:
            confidence_text = "The model shows good confidence in this classification."
        else:
            confidence_text = "The model shows moderate confidence in this classification."
        
        # Add key phrases context
        phrases_text = ""
        if key_phrases:
            phrases_text = f" Key indicators include: {', '.join(key_phrases[:3])}."
        
        return f"{base_explanation} {confidence_text}{phrases_text}"
    
    def analyze_sentiment(self, text: str) -> Dict:
        """Perform comprehensive sentiment analysis with explanations."""
        try:
            # Clean the input text
            cleaned_text = self.clean_text(text)
            
            if not cleaned_text:
                return {
                    'error': 'Empty or invalid text provided',
                    'text': text
                }
            
            # Tokenize the text
            inputs = self.tokenizer(
                cleaned_text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=self.max_length
            )
            
            # Move inputs to device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get predictions
            with torch.no_grad():
                outputs = self.model(**inputs)
                predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
                predicted_class = torch.argmax(predictions, dim=-1).item()
                confidence = predictions[0][predicted_class].item()
            
            # Get sentiment label
            sentiment = self.label_mapping[predicted_class]
            
            # Generate summary
            summary = self.generate_summary(cleaned_text)
            
            # Extract key phrases
            key_phrases = self.extract_key_phrases(cleaned_text, sentiment)
            
            # Generate explanation
            explanation = self.explain_sentiment(cleaned_text, sentiment, confidence, key_phrases)
            
            # Get all class probabilities
            all_probabilities = {
                self.label_mapping[i]: predictions[0][i].item()
                for i in range(len(self.label_mapping))
            }
            
            return {
                'text': cleaned_text,
                'sentiment': sentiment,
                'confidence': confidence,
                'summary': summary,
                'explanation': explanation,
                'key_phrases': key_phrases,
                'all_probabilities': all_probabilities,
                'analysis_timestamp': datetime.now().isoformat(),
                'text_length': len(cleaned_text),
                'word_count': len(cleaned_text.split())
            }
            
        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            return {
                'error': f'Analysis failed: {str(e)}',
                'text': text
            }

# Initialize the analyzer
def find_model_path():
    """Find the best available model path."""
    possible_paths = [
        "./finbert-sentiment-final",  # From notebook training
        "../models/final/finbert_pretrained",
        "../models/checkpoints/checkpoint-285",  # Latest checkpoint
        "../models/checkpoints/checkpoint-190",  # Earlier checkpoint
        "./models/final/finbert_pretrained",
        "./models/checkpoints/checkpoint-285",
        "./models/checkpoints/checkpoint-190"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            config_path = os.path.join(path, "config.json")
            tokenizer_path = os.path.join(path, "tokenizer_config.json")
            if os.path.exists(config_path) and os.path.exists(tokenizer_path):
                logger.info(f"Found valid model at: {path}")
                return path
    
    return None

MODEL_PATH = find_model_path()
if MODEL_PATH:
    try:
        analyzer = AdvancedFinBERTAnalyzer(MODEL_PATH)
        logger.info(f"FinBERT analyzer initialized successfully from {MODEL_PATH}")
    except Exception as e:
        logger.error(f"Failed to initialize analyzer: {e}")
        analyzer = None
else:
    logger.error("No valid model found. Please ensure the model is trained and saved.")
    analyzer = None

@app.route('/')
def home():
    """Main page with sentiment analysis interface."""
    return render_template('index.html')

@app.route('/analyze', methods=['POST'])
def analyze():
    """API endpoint for sentiment analysis."""
    try:
        if not analyzer:
            return jsonify({
                'error': 'Model not loaded. Please check the model path and try again.'
            }), 500
        
        # Get text from request
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({
                'error': 'No text provided. Please enter some text to analyze.'
            }), 400
        
        text = data['text'].strip()
        if not text:
            return jsonify({
                'error': 'Empty text provided. Please enter some text to analyze.'
            }), 400
        
        # Perform analysis
        result = analyzer.analyze_sentiment(text)
        
        if 'error' in result:
            return jsonify(result), 400
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in analyze endpoint: {e}")
        return jsonify({
            'error': f'Internal server error: {str(e)}'
        }), 500

@app.route('/health')
def health():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy' if analyzer else 'unhealthy',
        'model_loaded': analyzer is not None,
        'device': str(analyzer.device) if analyzer else 'N/A',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/info')
def model_info():
    """Get model information."""
    if not analyzer:
        return jsonify({'error': 'Model not loaded'}), 500
    
    return jsonify({
        'model_name': 'FinBERT Fine-tuned for Financial Sentiment Analysis',
        'labels': list(analyzer.label_mapping.values()),
        'max_length': analyzer.max_length,
        'device': str(analyzer.device),
        'available': True
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
